import numpy as np
import ctypes

# These constants are directly obtained from:
# https://github.com/numpy/numpy/blob/caccd283941b0bade7b71056138ded5379b1625f/numpy/random/src/distributions/ziggurat_constants.h

ki_double = np.array([
    0x000EF33D8025EF6A, 0x0000000000000000, 0x000C08BE98FBC6A8,
    0x000DA354FABD8142, 0x000E51F67EC1EEEA, 0x000EB255E9D3F77E,
    0x000EEF4B817ECAB9, 0x000F19470AFA44AA, 0x000F37ED61FFCB18,
    0x000F4F469561255C, 0x000F61A5E41BA396, 0x000F707A755396A4,
    0x000F7CB2EC28449A, 0x000F86F10C6357D3, 0x000F8FA6578325DE,
    0x000F9724C74DD0DA, 0x000F9DA907DBF509, 0x000FA360F581FA74,
    0x000FA86FDE5B4BF8, 0x000FACF160D354DC, 0x000FB0FB6718B90F,
    0x000FB49F8D5374C6, 0x000FB7EC2366FE77, 0x000FBAECE9A1E50E,
    0x000FBDAB9D040BED, 0x000FC03060FF6C57, 0x000FC2821037A248,
    0x000FC4A67AE25BD1, 0x000FC6A2977AEE31, 0x000FC87AA92896A4,
    0x000FCA325E4BDE85, 0x000FCBCCE902231A, 0x000FCD4D12F839C4,
    0x000FCEB54D8FEC99, 0x000FD007BF1DC930, 0x000FD1464DD6C4E6,
    0x000FD272A8E2F450, 0x000FD38E4FF0C91E, 0x000FD49A9990B478,
    0x000FD598B8920F53, 0x000FD689C08E99EC, 0x000FD76EA9C8E832,
    0x000FD848547B08E8, 0x000FD9178BAD2C8C, 0x000FD9DD07A7ADD2,
    0x000FDA9970105E8C, 0x000FDB4D5DC02E20, 0x000FDBF95C5BFCD0,
    0x000FDC9DEBB99A7D, 0x000FDD3B8118729D, 0x000FDDD288342F90,
    0x000FDE6364369F64, 0x000FDEEE708D514E, 0x000FDF7401A6B42E,
    0x000FDFF46599ED40, 0x000FE06FE4BC24F2, 0x000FE0E6C225A258,
    0x000FE1593C28B84C, 0x000FE1C78CBC3F99, 0x000FE231E9DB1CAA,
    0x000FE29885DA1B91, 0x000FE2FB8FB54186, 0x000FE35B33558D4A,
    0x000FE3B799D0002A, 0x000FE410E99EAD7F, 0x000FE46746D47734,
    0x000FE4BAD34C095C, 0x000FE50BAED29524, 0x000FE559F74EBC78,
    0x000FE5A5C8E41212, 0x000FE5EF3E138689, 0x000FE6366FD91078,
    0x000FE67B75C6D578, 0x000FE6BE661E11AA, 0x000FE6FF55E5F4F2,
    0x000FE73E5900A702, 0x000FE77B823E9E39, 0x000FE7B6E37070A2,
    0x000FE7F08D774243, 0x000FE8289053F08C, 0x000FE85EFB35173A,
    0x000FE893DC840864, 0x000FE8C741F0CEBC, 0x000FE8F9387D4EF6,
    0x000FE929CC879B1D, 0x000FE95909D388EA, 0x000FE986FB939AA2,
    0x000FE9B3AC714866, 0x000FE9DF2694B6D5, 0x000FEA0973ABE67C,
    0x000FEA329CF166A4, 0x000FEA5AAB32952C, 0x000FEA81A6D5741A,
    0x000FEAA797DE1CF0, 0x000FEACC85F3D920, 0x000FEAF07865E63C,
    0x000FEB13762FEC13, 0x000FEB3585FE2A4A, 0x000FEB56AE3162B4,
    0x000FEB76F4E284FA, 0x000FEB965FE62014, 0x000FEBB4F4CF9D7C,
    0x000FEBD2B8F449D0, 0x000FEBEFB16E2E3E, 0x000FEC0BE31EBDE8,
    0x000FEC2752B15A15, 0x000FEC42049DAFD3, 0x000FEC5BFD29F196,
    0x000FEC75406CEEF4, 0x000FEC8DD2500CB4, 0x000FECA5B6911F12,
    0x000FECBCF0C427FE, 0x000FECD38454FB15, 0x000FECE97488C8B3,
    0x000FECFEC47F91B7, 0x000FED1377358528, 0x000FED278F844903,
    0x000FED3B10242F4C, 0x000FED4DFBAD586E, 0x000FED605498C3DD,
    0x000FED721D414FE8, 0x000FED8357E4A982, 0x000FED9406A42CC8,
    0x000FEDA42B85B704, 0x000FEDB3C8746AB4, 0x000FEDC2DF416652,
    0x000FEDD171A46E52, 0x000FEDDF813C8AD3, 0x000FEDED0F909980,
    0x000FEDFA1E0FD414, 0x000FEE06AE124BC4, 0x000FEE12C0D95A06,
    0x000FEE1E579006E0, 0x000FEE29734B6524, 0x000FEE34150AE4BC,
    0x000FEE3E3DB89B3C, 0x000FEE47EE2982F4, 0x000FEE51271DB086,
    0x000FEE59E9407F41, 0x000FEE623528B42E, 0x000FEE6A0B5897F1,
    0x000FEE716C3E077A, 0x000FEE7858327B82, 0x000FEE7ECF7B06BA,
    0x000FEE84D2484AB2, 0x000FEE8A60B66343, 0x000FEE8F7ACCC851,
    0x000FEE94207E25DA, 0x000FEE9851A829EA, 0x000FEE9C0E13485C,
    0x000FEE9F557273F4, 0x000FEEA22762CCAE, 0x000FEEA4836B42AC,
    0x000FEEA668FC2D71, 0x000FEEA7D76ED6FA, 0x000FEEA8CE04FA0A,
    0x000FEEA94BE8333B, 0x000FEEA950296410, 0x000FEEA8D9C0075E,
    0x000FEEA7E7897654, 0x000FEEA678481D24, 0x000FEEA48AA29E83,
    0x000FEEA21D22E4DA, 0x000FEE9F2E352024, 0x000FEE9BBC26AF2E,
    0x000FEE97C524F2E4, 0x000FEE93473C0A3A, 0x000FEE8E40557516,
    0x000FEE88AE369C7A, 0x000FEE828E7F3DFD, 0x000FEE7BDEA7B888,
    0x000FEE749BFF37FF, 0x000FEE6CC3A9BD5E, 0x000FEE64529E007E,
    0x000FEE5B45A32888, 0x000FEE51994E57B6, 0x000FEE474A0006CF,
    0x000FEE3C53E12C50, 0x000FEE30B2E02AD8, 0x000FEE2462AD8205,
    0x000FEE175EB83C5A, 0x000FEE09A22A1447, 0x000FEDFB27E349CC,
    0x000FEDEBEA76216C, 0x000FEDDBE422047E, 0x000FEDCB0ECE39D3,
    0x000FEDB964042CF4, 0x000FEDA6DCE938C9, 0x000FED937237E98D,
    0x000FED7F1C38A836, 0x000FED69D2B9C02B, 0x000FED538D06AE00,
    0x000FED3C41DEA422, 0x000FED23E76A2FD8, 0x000FED0A732FE644,
    0x000FECEFDA07FE34, 0x000FECD4100EB7B8, 0x000FECB708956EB4,
    0x000FEC98B61230C1, 0x000FEC790A0DA978, 0x000FEC57F50F31FE,
    0x000FEC356686C962, 0x000FEC114CB4B335, 0x000FEBEB948E6FD0,
    0x000FEBC429A0B692, 0x000FEB9AF5EE0CDC, 0x000FEB6FE1C98542,
    0x000FEB42D3AD1F9E, 0x000FEB13B00B2D4B, 0x000FEAE2591A02E9,
    0x000FEAAEAE992257, 0x000FEA788D8EE326, 0x000FEA3FCFFD73E5,
    0x000FEA044C8DD9F6, 0x000FE9C5D62F563B, 0x000FE9843BA947A4,
    0x000FE93F471D4728, 0x000FE8F6BD76C5D6, 0x000FE8AA5DC4E8E6,
    0x000FE859E07AB1EA, 0x000FE804F690A940, 0x000FE7AB488233C0,
    0x000FE74C751F6AA5, 0x000FE6E8102AA202, 0x000FE67DA0B6ABD8,
    0x000FE60C9F38307E, 0x000FE5947338F742, 0x000FE51470977280,
    0x000FE48BD436F458, 0x000FE3F9BFFD1E37, 0x000FE35D35EEB19C,
    0x000FE2B5122FE4FE, 0x000FE20003995557, 0x000FE13C82788314,
    0x000FE068C4EE67B0, 0x000FDF82B02B71AA, 0x000FDE87C57EFEAA,
    0x000FDD7509C63BFD, 0x000FDC46E529BF13, 0x000FDAF8F82E0282,
    0x000FD985E1B2BA75, 0x000FD7E6EF48CF04, 0x000FD613ADBD650B,
    0x000FD40149E2F012, 0x000FD1A1A7B4C7AC, 0x000FCEE204761F9E,
    0x000FCBA8D85E11B2, 0x000FC7D26ECD2D22, 0x000FC32B2F1E22ED,
    0x000FBD6581C0B83A, 0x000FB606C4005434, 0x000FAC40582A2874,
    0x000F9E971E014598, 0x000F89FA48A41DFC, 0x000F66C5F7F0302C,
    0x000F1A5A4B331C4A], dtype=np.uint64)

wi_double = np.array([
    8.68362706080130616677e-16, 4.77933017572773682428e-17,
    6.35435241740526230246e-17, 7.45487048124769627714e-17,
    8.32936681579309972857e-17, 9.06806040505948228243e-17,
    9.71486007656776183958e-17, 1.02947503142410192108e-16,
    1.08234302884476839838e-16, 1.13114701961090307945e-16,
    1.17663594570229211411e-16, 1.21936172787143633280e-16,
    1.25974399146370927864e-16, 1.29810998862640315416e-16,
    1.33472037368241227547e-16, 1.36978648425712032797e-16,
    1.40348230012423820659e-16, 1.43595294520569430270e-16,
    1.46732087423644219083e-16, 1.49769046683910367425e-16,
    1.52715150035961979750e-16, 1.55578181694607639484e-16,
    1.58364940092908853989e-16, 1.61081401752749279325e-16,
    1.63732852039698532012e-16, 1.66323990584208352778e-16,
    1.68859017086765964015e-16, 1.71341701765596607184e-16,
    1.73775443658648593310e-16, 1.76163319230009959832e-16,
    1.78508123169767272927e-16, 1.80812402857991522674e-16,
    1.83078487648267501776e-16, 1.85308513886180189386e-16,
    1.87504446393738816849e-16, 1.89668097007747596212e-16,
    1.91801140648386198029e-16, 1.93905129306251037069e-16,
    1.95981504266288244037e-16, 1.98031606831281739736e-16,
    2.00056687762733300198e-16, 2.02057915620716538808e-16,
    2.04036384154802118313e-16, 2.05993118874037063144e-16,
    2.07929082904140197311e-16, 2.09845182223703516690e-16,
    2.11742270357603418769e-16, 2.13621152594498681022e-16,
    2.15482589785814580926e-16, 2.17327301775643674990e-16,
    2.19155970504272708519e-16, 2.20969242822353175995e-16,
    2.22767733047895534948e-16, 2.24552025294143552381e-16,
    2.26322675592856786566e-16, 2.28080213834501706782e-16,
    2.29825145544246839061e-16, 2.31557953510408037008e-16,
    2.33279099280043561128e-16, 2.34989024534709550938e-16,
    2.36688152357916037468e-16, 2.38376888404542434981e-16,
    2.40055621981350627349e-16, 2.41724727046750252175e-16,
    2.43384563137110286400e-16, 2.45035476226149539878e-16,
    2.46677799523270498158e-16, 2.48311854216108767769e-16,
    2.49937950162045242375e-16, 2.51556386532965786439e-16,
    2.53167452417135826983e-16, 2.54771427381694417303e-16,
    2.56368581998939683749e-16, 2.57959178339286723500e-16,
    2.59543470433517070146e-16, 2.61121704706701939097e-16,
    2.62694120385972564623e-16, 2.64260949884118951286e-16,
    2.65822419160830680292e-16, 2.67378748063236329361e-16,
    2.68930150647261591777e-16, 2.70476835481199518794e-16,
    2.72019005932773206655e-16, 2.73556860440867908686e-16,
    2.75090592773016664571e-16, 2.76620392269639032183e-16,
    2.78146444075954410103e-16, 2.79668929362423005309e-16,
    2.81188025534502074329e-16, 2.82703906432447923059e-16,
    2.84216742521840606520e-16, 2.85726701075460149289e-16,
    2.87233946347097994381e-16, 2.88738639737848191815e-16,
    2.90240939955384233230e-16, 2.91741003166694553259e-16,
    2.93238983144718163965e-16, 2.94735031409293489611e-16,
    2.96229297362806647792e-16, 2.97721928420902891115e-16,
    2.99213070138601307081e-16, 3.00702866332133102993e-16,
    3.02191459196806151971e-16, 3.03678989421180184427e-16,
    3.05165596297821922381e-16, 3.06651417830895451744e-16,
    3.08136590840829717032e-16, 3.09621251066292253306e-16,
    3.11105533263689296831e-16, 3.12589571304399892784e-16,
    3.14073498269944617203e-16, 3.15557446545280064031e-16,
    3.17041547910402852545e-16, 3.18525933630440648871e-16,
    3.20010734544401137886e-16, 3.21496081152744704901e-16,
    3.22982103703941557538e-16, 3.24468932280169778077e-16,
    3.25956696882307838340e-16, 3.27445527514370671802e-16,
    3.28935554267536967851e-16, 3.30426907403912838589e-16,
    3.31919717440175233652e-16, 3.33414115231237245918e-16,
    3.34910232054077845412e-16, 3.36408199691876507948e-16,
    3.37908150518594979994e-16, 3.39410217584148914282e-16,
    3.40914534700312603713e-16, 3.42421236527501816058e-16,
    3.43930458662583133920e-16, 3.45442337727858401604e-16,
    3.46957011461378353333e-16, 3.48474618808741370700e-16,
    3.49995300016538099813e-16, 3.51519196727607440975e-16,
    3.53046452078274009054e-16, 3.54577210797743572160e-16,
    3.56111619309838843415e-16, 3.57649825837265051035e-16,
    3.59191980508602994994e-16, 3.60738235468235137839e-16,
    3.62288744989419151904e-16, 3.63843665590734438546e-16,
    3.65403156156136995766e-16, 3.66967378058870090021e-16,
    3.68536495289491401456e-16, 3.70110674588289834952e-16,
    3.71690085582382297792e-16, 3.73274900927794352614e-16,
    3.74865296456848868882e-16, 3.76461451331202869131e-16,
    3.78063548200896037651e-16, 3.79671773369794425924e-16,
    3.81286316967837738238e-16, 3.82907373130524317507e-16,
    3.84535140186095955858e-16, 3.86169820850914927119e-16,
    3.87811622433558721164e-16, 3.89460757048192620674e-16,
    3.91117441837820542060e-16, 3.92781899208054153270e-16,
    3.94454357072087711446e-16, 3.96135049107613542983e-16,
    3.97824215026468259474e-16, 3.99522100857856502444e-16,
    4.01228959246062907451e-16, 4.02945049763632792393e-16,
    4.04670639241074995115e-16, 4.06406002114225038723e-16,
    4.08151420790493873480e-16, 4.09907186035326643447e-16,
    4.11673597380302570170e-16, 4.13450963554423599878e-16,
    4.15239602940268833891e-16, 4.17039844056831587498e-16,
    4.18852026071011229572e-16, 4.20676499339901510978e-16,
    4.22513625986204937320e-16, 4.24363780509307796137e-16,
    4.26227350434779809917e-16, 4.28104737005311666397e-16,
    4.29996355916383230161e-16, 4.31902638100262944617e-16,
    4.33824030562279080411e-16, 4.35760997273684900553e-16,
    4.37714020125858747008e-16, 4.39683599951052137423e-16,
    4.41670257615420348435e-16, 4.43674535190656726604e-16,
    4.45696997211204306674e-16, 4.47738232024753387312e-16,
    4.49798853244554968009e-16, 4.51879501313005876278e-16,
    4.53980845187003400947e-16, 4.56103584156742206384e-16,
    4.58248449810956667052e-16, 4.60416208163115281428e-16,
    4.62607661954784567754e-16, 4.64823653154320737780e-16,
    4.67065065671263059081e-16, 4.69332828309332890697e-16,
    4.71627917983835129766e-16, 4.73951363232586715165e-16,
    4.76304248053313737663e-16, 4.78687716104872284247e-16,
    4.81102975314741720538e-16, 4.83551302941152515162e-16,
    4.86034051145081195402e-16, 4.88552653135360343280e-16,
    4.91108629959526955862e-16, 4.93703598024033454728e-16,
    4.96339277440398725619e-16, 4.99017501309182245754e-16,
    5.01740226071808946011e-16, 5.04509543081872748637e-16,
    5.07327691573354207058e-16, 5.10197073234156184149e-16,
    5.13120268630678373200e-16, 5.16100055774322824569e-16,
    5.19139431175769859873e-16, 5.22241633800023428760e-16,
    5.25410172417759732697e-16, 5.28648856950494511482e-16,
    5.31961834533840037535e-16, 5.35353631181649688145e-16,
    5.38829200133405320160e-16, 5.42393978220171234073e-16,
    5.46053951907478041166e-16, 5.49815735089281410703e-16,
    5.53686661246787600374e-16, 5.57674893292657647836e-16,
    5.61789555355541665830e-16, 5.66040892008242216739e-16,
    5.70440462129138908417e-16, 5.75001376891989523684e-16,
    5.79738594572459365014e-16, 5.84669289345547900201e-16,
    5.89813317647789942685e-16, 5.95193814964144415532e-16,
    6.00837969627190832234e-16, 6.06778040933344851394e-16,
    6.13052720872528159123e-16, 6.19708989458162555387e-16,
    6.26804696330128439415e-16, 6.34412240712750598627e-16,
    6.42623965954805540945e-16, 6.51560331734499356881e-16,
    6.61382788509766415145e-16, 6.72315046250558662913e-16,
    6.84680341756425875856e-16, 6.98971833638761995415e-16,
    7.15999493483066421560e-16, 7.37242430179879890722e-16,
    7.65893637080557275482e-16, 8.11384933765648418565e-16],
    dtype=np.float64)

fi_double = np.array([
    1.00000000000000000000e+00, 9.77101701267671596263e-01,
    9.59879091800106665211e-01, 9.45198953442299649730e-01,
    9.32060075959230460718e-01, 9.19991505039347012840e-01,
    9.08726440052130879366e-01, 8.98095921898343418910e-01,
    8.87984660755833377088e-01, 8.78309655808917399966e-01,
    8.69008688036857046555e-01, 8.60033621196331532488e-01,
    8.51346258458677951353e-01, 8.42915653112204177333e-01,
    8.34716292986883434679e-01, 8.26726833946221373317e-01,
    8.18929191603702366642e-01, 8.11307874312656274185e-01,
    8.03849483170964274059e-01, 7.96542330422958966274e-01,
    7.89376143566024590648e-01, 7.82341832654802504798e-01,
    7.75431304981187174974e-01, 7.68637315798486264740e-01,
    7.61953346836795386565e-01, 7.55373506507096115214e-01,
    7.48892447219156820459e-01, 7.42505296340151055290e-01,
    7.36207598126862650112e-01, 7.29995264561476231435e-01,
    7.23864533468630222401e-01, 7.17811932630721960535e-01,
    7.11834248878248421200e-01, 7.05928501332754310127e-01,
    7.00091918136511615067e-01, 6.94321916126116711609e-01,
    6.88616083004671808432e-01, 6.82972161644994857355e-01,
    6.77388036218773526009e-01, 6.71861719897082099173e-01,
    6.66391343908750100056e-01, 6.60975147776663107813e-01,
    6.55611470579697264149e-01, 6.50298743110816701574e-01,
    6.45035480820822293424e-01, 6.39820277453056585060e-01,
    6.34651799287623608059e-01, 6.29528779924836690007e-01,
    6.24450015547026504592e-01, 6.19414360605834324325e-01,
    6.14420723888913888899e-01, 6.09468064925773433949e-01,
    6.04555390697467776029e-01, 5.99681752619125263415e-01,
    5.94846243767987448159e-01, 5.90047996332826008015e-01,
    5.85286179263371453274e-01, 5.80559996100790898232e-01,
    5.75868682972353718164e-01, 5.71211506735253227163e-01,
    5.66587763256164445025e-01, 5.61996775814524340831e-01,
    5.57437893618765945014e-01, 5.52910490425832290562e-01,
    5.48413963255265812791e-01, 5.43947731190026262382e-01,
    5.39511234256952132426e-01, 5.35103932380457614215e-01,
    5.30725304403662057062e-01, 5.26374847171684479008e-01,
    5.22052074672321841931e-01, 5.17756517229756352272e-01,
    5.13487720747326958914e-01, 5.09245245995747941592e-01,
    5.05028667943468123624e-01, 5.00837575126148681903e-01,
    4.96671569052489714213e-01, 4.92530263643868537748e-01,
    4.88413284705458028423e-01, 4.84320269426683325253e-01,
    4.80250865909046753544e-01, 4.76204732719505863248e-01,
    4.72181538467730199660e-01, 4.68180961405693596422e-01,
    4.64202689048174355069e-01, 4.60246417812842867345e-01,
    4.56311852678716434184e-01, 4.52398706861848520777e-01,
    4.48506701507203064949e-01, 4.44635565395739396077e-01,
    4.40785034665803987508e-01, 4.36954852547985550526e-01,
    4.33144769112652261445e-01, 4.29354541029441427735e-01,
    4.25583931338021970170e-01, 4.21832709229495894654e-01,
    4.18100649837848226120e-01, 4.14387534040891125642e-01,
    4.10693148270188157500e-01, 4.07017284329473372217e-01,
    4.03359739221114510510e-01, 3.99720314980197222177e-01,
    3.96098818515832451492e-01, 3.92495061459315619512e-01,
    3.88908860018788715696e-01, 3.85340034840077283462e-01,
    3.81788410873393657674e-01, 3.78253817245619183840e-01,
    3.74736087137891138443e-01, 3.71235057668239498696e-01,
    3.67750569779032587814e-01, 3.64282468129004055601e-01,
    3.60830600989648031529e-01, 3.57394820145780500731e-01,
    3.53974980800076777232e-01, 3.50570941481406106455e-01,
    3.47182563956793643900e-01, 3.43809713146850715049e-01,
    3.40452257044521866547e-01, 3.37110066637006045021e-01,
    3.33783015830718454708e-01, 3.30470981379163586400e-01,
    3.27173842813601400970e-01, 3.23891482376391093290e-01,
    3.20623784956905355514e-01, 3.17370638029913609834e-01,
    3.14131931596337177215e-01, 3.10907558126286509559e-01,
    3.07697412504292056035e-01, 3.04501391976649993243e-01,
    3.01319396100803049698e-01, 2.98151326696685481377e-01,
    2.94997087799961810184e-01, 2.91856585617095209972e-01,
    2.88729728482182923521e-01, 2.85616426815501756042e-01,
    2.82516593083707578948e-01, 2.79430141761637940157e-01,
    2.76356989295668320494e-01, 2.73297054068577072172e-01,
    2.70250256365875463072e-01, 2.67216518343561471038e-01,
    2.64195763997261190426e-01, 2.61187919132721213522e-01,
    2.58192911337619235290e-01, 2.55210669954661961700e-01,
    2.52241126055942177508e-01, 2.49284212418528522415e-01,
    2.46339863501263828249e-01, 2.43408015422750312329e-01,
    2.40488605940500588254e-01, 2.37581574431238090606e-01,
    2.34686861872330010392e-01, 2.31804410824338724684e-01,
    2.28934165414680340644e-01, 2.26076071322380278694e-01,
    2.23230075763917484855e-01, 2.20396127480151998723e-01,
    2.17574176724331130872e-01, 2.14764175251173583536e-01,
    2.11966076307030182324e-01, 2.09179834621125076977e-01,
    2.06405406397880797353e-01, 2.03642749310334908452e-01,
    2.00891822494656591136e-01, 1.98152586545775138971e-01,
    1.95425003514134304483e-01, 1.92709036903589175926e-01,
    1.90004651670464985713e-01, 1.87311814223800304768e-01,
    1.84630492426799269756e-01, 1.81960655599522513892e-01,
    1.79302274522847582272e-01, 1.76655321443734858455e-01,
    1.74019770081838553999e-01, 1.71395595637505754327e-01,
    1.68782774801211288285e-01, 1.66181285764481906364e-01,
    1.63591108232365584074e-01, 1.61012223437511009516e-01,
    1.58444614155924284882e-01, 1.55888264724479197465e-01,
    1.53343161060262855866e-01, 1.50809290681845675763e-01,
    1.48286642732574552861e-01, 1.45775208005994028060e-01,
    1.43274978973513461566e-01, 1.40785949814444699690e-01,
    1.38308116448550733057e-01, 1.35841476571253755301e-01,
    1.33386029691669155683e-01, 1.30941777173644358090e-01,
    1.28508722279999570981e-01, 1.26086870220185887081e-01,
    1.23676228201596571932e-01, 1.21276805484790306533e-01,
    1.18888613442910059947e-01, 1.16511665625610869035e-01,
    1.14145977827838487895e-01, 1.11791568163838089811e-01,
    1.09448457146811797824e-01, 1.07116667774683801961e-01,
    1.04796225622487068629e-01, 1.02487158941935246892e-01,
    1.00189498768810017482e-01, 9.79032790388624646338e-02,
    9.56285367130089991594e-02, 9.33653119126910124859e-02,
    9.11136480663737591268e-02, 8.88735920682758862021e-02,
    8.66451944505580717859e-02, 8.44285095703534715916e-02,
    8.22235958132029043366e-02, 8.00305158146630696292e-02,
    7.78493367020961224423e-02, 7.56801303589271778804e-02,
    7.35229737139813238622e-02, 7.13779490588904025339e-02,
    6.92451443970067553879e-02, 6.71246538277884968737e-02,
    6.50165779712428976156e-02, 6.29210244377581412456e-02,
    6.08381083495398780614e-02, 5.87679529209337372930e-02,
    5.67106901062029017391e-02, 5.46664613248889208474e-02,
    5.26354182767921896513e-02, 5.06177238609477817000e-02,
    4.86135532158685421122e-02, 4.66230949019303814174e-02,
    4.46465522512944634759e-02, 4.26841449164744590750e-02,
    4.07361106559409394401e-02, 3.88027074045261474722e-02,
    3.68842156885673053135e-02, 3.49809414617161251737e-02,
    3.30932194585785779961e-02, 3.12214171919203004046e-02,
    2.93659397581333588001e-02, 2.75272356696031131329e-02,
    2.57058040085489103443e-02, 2.39022033057958785407e-02,
    2.21170627073088502113e-02, 2.03510962300445102935e-02,
    1.86051212757246224594e-02, 1.68800831525431419000e-02,
    1.51770883079353092332e-02, 1.34974506017398673818e-02,
    1.18427578579078790488e-02, 1.02149714397014590439e-02,
    8.61658276939872638800e-03, 7.05087547137322242369e-03,
    5.52240329925099155545e-03, 4.03797259336302356153e-03,
    2.60907274610215926189e-03, 1.26028593049859797236e-03],
    dtype=np.float64)

ki_float = np.array([
    0x007799EC, 0x00000000, 0x006045F5, 0x006D1AA8, 0x00728FB4,
    0x007592AF, 0x00777A5C, 0x0078CA38, 0x0079BF6B, 0x007A7A35,
    0x007B0D2F, 0x007B83D4, 0x007BE597, 0x007C3788, 0x007C7D33,
    0x007CB926, 0x007CED48, 0x007D1B08, 0x007D437F, 0x007D678B,
    0x007D87DB, 0x007DA4FC, 0x007DBF61, 0x007DD767, 0x007DED5D,
    0x007E0183, 0x007E1411, 0x007E2534, 0x007E3515, 0x007E43D5,
    0x007E5193, 0x007E5E67, 0x007E6A69, 0x007E75AA, 0x007E803E,
    0x007E8A32, 0x007E9395, 0x007E9C72, 0x007EA4D5, 0x007EACC6,
    0x007EB44E, 0x007EBB75, 0x007EC243, 0x007EC8BC, 0x007ECEE8,
    0x007ED4CC, 0x007EDA6B, 0x007EDFCB, 0x007EE4EF, 0x007EE9DC,
    0x007EEE94, 0x007EF31B, 0x007EF774, 0x007EFBA0, 0x007EFFA3,
    0x007F037F, 0x007F0736, 0x007F0ACA, 0x007F0E3C, 0x007F118F,
    0x007F14C4, 0x007F17DC, 0x007F1ADA, 0x007F1DBD, 0x007F2087,
    0x007F233A, 0x007F25D7, 0x007F285D, 0x007F2AD0, 0x007F2D2E,
    0x007F2F7A, 0x007F31B3, 0x007F33DC, 0x007F35F3, 0x007F37FB,
    0x007F39F3, 0x007F3BDC, 0x007F3DB7, 0x007F3F84, 0x007F4145,
    0x007F42F8, 0x007F449F, 0x007F463A, 0x007F47CA, 0x007F494E,
    0x007F4AC8, 0x007F4C38, 0x007F4D9D, 0x007F4EF9, 0x007F504C,
    0x007F5195, 0x007F52D5, 0x007F540D, 0x007F553D, 0x007F5664,
    0x007F5784, 0x007F589C, 0x007F59AC, 0x007F5AB5, 0x007F5BB8,
    0x007F5CB3, 0x007F5DA8, 0x007F5E96, 0x007F5F7E, 0x007F605F,
    0x007F613B, 0x007F6210, 0x007F62E0, 0x007F63AA, 0x007F646F,
    0x007F652E, 0x007F65E8, 0x007F669C, 0x007F674C, 0x007F67F6,
    0x007F689C, 0x007F693C, 0x007F69D9, 0x007F6A70, 0x007F6B03,
    0x007F6B91, 0x007F6C1B, 0x007F6CA0, 0x007F6D21, 0x007F6D9E,
    0x007F6E17, 0x007F6E8C, 0x007F6EFC, 0x007F6F68, 0x007F6FD1,
    0x007F7035, 0x007F7096, 0x007F70F3, 0x007F714C, 0x007F71A1,
    0x007F71F2, 0x007F723F, 0x007F7289, 0x007F72CF, 0x007F7312,
    0x007F7350, 0x007F738B, 0x007F73C3, 0x007F73F6, 0x007F7427,
    0x007F7453, 0x007F747C, 0x007F74A1, 0x007F74C3, 0x007F74E0,
    0x007F74FB, 0x007F7511, 0x007F7524, 0x007F7533, 0x007F753F,
    0x007F7546, 0x007F754A, 0x007F754B, 0x007F7547, 0x007F753F,
    0x007F7534, 0x007F7524, 0x007F7511, 0x007F74F9, 0x007F74DE,
    0x007F74BE, 0x007F749A, 0x007F7472, 0x007F7445, 0x007F7414,
    0x007F73DF, 0x007F73A5, 0x007F7366, 0x007F7323, 0x007F72DA,
    0x007F728D, 0x007F723A, 0x007F71E3, 0x007F7186, 0x007F7123,
    0x007F70BB, 0x007F704D, 0x007F6FD9, 0x007F6F5F, 0x007F6EDF,
    0x007F6E58, 0x007F6DCB, 0x007F6D37, 0x007F6C9C, 0x007F6BF9,
    0x007F6B4F, 0x007F6A9C, 0x007F69E2, 0x007F691F, 0x007F6854,
    0x007F677F, 0x007F66A1, 0x007F65B8, 0x007F64C6, 0x007F63C8,
    0x007F62C0, 0x007F61AB, 0x007F608A, 0x007F5F5D, 0x007F5E21,
    0x007F5CD8, 0x007F5B7F, 0x007F5A17, 0x007F589E, 0x007F5713,
    0x007F5575, 0x007F53C4, 0x007F51FE, 0x007F5022, 0x007F4E2F,
    0x007F4C22, 0x007F49FA, 0x007F47B6, 0x007F4553, 0x007F42CF,
    0x007F4028, 0x007F3D5A, 0x007F3A64, 0x007F3741, 0x007F33ED,
    0x007F3065, 0x007F2CA4, 0x007F28A4, 0x007F245F, 0x007F1FCE,
    0x007F1AEA, 0x007F15A9, 0x007F1000, 0x007F09E4, 0x007F0346,
    0x007EFC16, 0x007EF43E, 0x007EEBA8, 0x007EE237, 0x007ED7C8,
    0x007ECC2F, 0x007EBF37, 0x007EB09D, 0x007EA00A, 0x007E8D0D,
    0x007E7710, 0x007E5D47, 0x007E3E93, 0x007E1959, 0x007DEB2C,
    0x007DB036, 0x007D6203, 0x007CF4B9, 0x007C4FD2, 0x007B3630,
    0x0078D2D2], dtype=np.uint32)

wi_float = np.array([
    4.66198677960027669255e-07, 2.56588335019207033255e-08,
    3.41146697750176784592e-08, 4.00230311410932959821e-08,
    4.47179475877737745459e-08, 4.86837785973537366722e-08,
    5.21562578925932412861e-08, 5.52695199001886257153e-08,
    5.81078488992733116465e-08, 6.07279932024587421409e-08,
    6.31701613261172047795e-08, 6.54639842900233842742e-08,
    6.76319905583641815324e-08, 6.96917493470166688656e-08,
    7.16572544283857476692e-08, 7.35398519048393832969e-08,
    7.53488822443557479279e-08, 7.70921367281667127885e-08,
    7.87761895947956022626e-08, 8.04066446825615346857e-08,
    8.19883218760237408659e-08, 8.35254002936857088917e-08,
    8.50215298165053411740e-08, 8.64799190652369040985e-08,
    8.79034055989140110861e-08, 8.92945125124233511541e-08,
    9.06554945027956262312e-08, 9.19883756905278607229e-08,
    9.32949809202232869780e-08, 9.45769618559625849039e-08,
    9.58358188855612866442e-08, 9.70729196232813152662e-08,
    9.82895146313061088986e-08, 9.94867508514382224721e-08,
    1.00665683139461669691e-07, 1.01827284217853923044e-07,
    1.02972453302539369464e-07, 1.04102023612124921572e-07,
    1.05216768930574060431e-07, 1.06317409364335657741e-07,
    1.07404616410877866490e-07, 1.08479017436113134283e-07,
    1.09541199642370962438e-07, 1.10591713595628691212e-07,
    1.11631076370069356306e-07, 1.12659774359245895023e-07,
    1.13678265795837113569e-07, 1.14686983015899673063e-07,
    1.15686334498432158725e-07, 1.16676706706789039179e-07,
    1.17658465754873988919e-07, 1.18631958917986203582e-07,
    1.19597516005596215528e-07, 1.20555450611113917226e-07,
    1.21506061251817163689e-07, 1.22449632410483948386e-07,
    1.23386435488872536840e-07, 1.24316729681986364321e-07,
    1.25240762781015530062e-07, 1.26158771911939892267e-07,
    1.27070984215989333455e-07, 1.27977617477468922011e-07,
    1.28878880703854958297e-07, 1.29774974662539874521e-07,
    1.30666092378141980504e-07, 1.31552419593887221722e-07,
    1.32434135200211397569e-07, 1.33311411633413359243e-07,
    1.34184415246907777059e-07, 1.35053306657377859830e-07,
    1.35918241067904315860e-07, 1.36779368569952053923e-07,
    1.37636834425917531047e-07, 1.38490779333783508675e-07,
    1.39341339675287344817e-07, 1.40188647748881762555e-07,
    1.41032831988654882776e-07, 1.41874017170273235693e-07,
    1.42712324604921442006e-07, 1.43547872322127921816e-07,
    1.44380775242292721080e-07, 1.45211145339665544509e-07,
    1.46039091796461362146e-07, 1.46864721148745476208e-07,
    1.47688137424670065700e-07, 1.48509442275598857119e-07,
    1.49328735100614641423e-07, 1.50146113164867617390e-07,
    1.50961671712187416111e-07, 1.51775504072350982845e-07,
    1.52587701763369746341e-07, 1.53398354589133671168e-07,
    1.54207550732725568797e-07, 1.55015376845697999657e-07,
    1.55821918133584372604e-07, 1.56627258437898192833e-07,
    1.57431480314857468671e-07, 1.58234665111056041043e-07,
    1.59036893036289199880e-07, 1.59838243233728855017e-07,
    1.60638793847630850137e-07, 1.61438622088746393909e-07,
    1.62237804297600106296e-07, 1.63036416005787357730e-07,
    1.63834531995435479082e-07, 1.64632226356965902954e-07,
    1.65429572545287097020e-07, 1.66226643434541294491e-07,
    1.67023511371523209274e-07, 1.67820248227882200051e-07,
    1.68616925451215588827e-07, 1.69413614115155757272e-07,
    1.70210384968549673733e-07, 1.71007308483826142122e-07,
    1.71804454904642543391e-07, 1.72601894292900061024e-07,
    1.73399696575213681990e-07, 1.74197931588920988271e-07,
    1.74996669127712165834e-07, 1.75795978986961275677e-07,
    1.76595931008838063924e-07, 1.77396595127278238022e-07,
    1.78198041412889183130e-07, 1.79000340117867431104e-07,
    1.79803561721004406185e-07, 1.80607776972855859813e-07,
    1.81413056941151359868e-07, 1.82219473056520464354e-07,
    1.83027097158612474240e-07, 1.83836001542687613069e-07,
    1.84646259006759307383e-07, 1.85457942899367347876e-07,
    1.86271127168064649331e-07, 1.87085886408701333260e-07,
    1.87902295915592424729e-07, 1.88720431732658022414e-07,
    1.89540370705627262627e-07, 1.90362190535400839128e-07,
    1.91185969832669990437e-07, 1.92011788173893651535e-07,
    1.92839726158739913768e-07, 1.93669865469102145482e-07,
    1.94502288929804890433e-07, 1.95337080571120616772e-07,
    1.96174325693223683314e-07, 1.97014110932714374919e-07,
    1.97856524331352952716e-07, 1.98701655407150388211e-07,
    1.99549595227971635348e-07, 2.00400436487814600236e-07,
    2.01254273585938820883e-07, 2.02111202709026498408e-07,
    2.02971321916571014951e-07, 2.03834731229698846698e-07,
    2.04701532723644121196e-07, 2.05571830624108885378e-07,
    2.06445731407757185541e-07, 2.07323343907107312957e-07,
    2.08204779420104330037e-07, 2.09090151824673600213e-07,
    2.09979577698577670508e-07, 2.10873176444920111011e-07,
    2.11771070423665379388e-07, 2.12673385089569268965e-07,
    2.13580249136944118603e-07, 2.14491794651713402832e-07,
    2.15408157271244625533e-07, 2.16329476352486921685e-07,
    2.17255895148978920488e-07, 2.18187560997337924713e-07,
    2.19124625513888206785e-07, 2.20067244802139479285e-07,
    2.21015579671883851683e-07, 2.21969795870742159701e-07,
    2.22930064329060010376e-07, 2.23896561419128954210e-07,
    2.24869469229791575583e-07, 2.25848975857580322189e-07,
    2.26835275715640744118e-07, 2.27828569861799901001e-07,
    2.28829066347263833069e-07, 2.29836980587561823183e-07,
    2.30852535757505260518e-07, 2.31875963212094114516e-07,
    2.32907502935486642699e-07, 2.33947404020352726160e-07,
    2.34995925180156140289e-07, 2.36053335297164516378e-07,
    2.37119914009265667728e-07, 2.38195952338983970691e-07,
    2.39281753368440712742e-07, 2.40377632964396957621e-07,
    2.41483920557958384709e-07, 2.42600959984018662258e-07,
    2.43729110386077326413e-07, 2.44868747192698939290e-07,
    2.46020263172594533433e-07, 2.47184069576113545901e-07,
    2.48360597371852893654e-07, 2.49550298588131851232e-07,
    2.50753647770270890721e-07, 2.51971143565970967140e-07,
    2.53203310452642767375e-07, 2.54450700622322097890e-07,
    2.55713896041856770961e-07, 2.56993510708419870887e-07,
    2.58290193123138874550e-07, 2.59604629008804833146e-07,
    2.60937544301314385690e-07, 2.62289708448800566945e-07,
    2.63661938057441759882e-07, 2.65055100928844238758e-07,
    2.66470120540847889467e-07, 2.67907981031821866252e-07,
    2.69369732758258246335e-07, 2.70856498507068313229e-07,
    2.72369480457841388042e-07, 2.73909968006952220135e-07,
    2.75479346585437289399e-07, 2.77079107626811561009e-07,
    2.78710859870496796972e-07, 2.80376342222588603820e-07,
    2.82077438439999912690e-07, 2.83816193958769527230e-07,
    2.85594835255375795814e-07, 2.87415792215003905739e-07,
    2.89281724087851835900e-07, 2.91195549750371467233e-07,
    2.93160483161771875581e-07, 2.95180075129332912389e-07,
    2.97258262785797916083e-07, 2.99399428561531794298e-07,
    3.01608470935804138388e-07, 3.03890889921758510417e-07,
    3.06252891144972267537e-07, 3.08701513613258141075e-07,
    3.11244787989714509378e-07, 3.13891934589336184321e-07,
    3.16653613755314681314e-07, 3.19542246256559459667e-07,
    3.22572428717978242099e-07, 3.25761480217458181578e-07,
    3.29130173358915628534e-07, 3.32703730345002116955e-07,
    3.36513208964639108346e-07, 3.40597478255417943913e-07,
    3.45006114675213401550e-07, 3.49803789521323211592e-07,
    3.55077180848341416206e-07, 3.60946392031859609868e-07,
    3.67584959507244041831e-07, 3.75257645787954431030e-07,
    3.84399301057791926300e-07, 3.95804015855768440983e-07,
    4.11186015434435801956e-07, 4.35608969373823260746e-07],
    dtype=np.float32)

fi_float = np.array([
    1.00000000000000000000e+00, 9.77101701267671596263e-01,
    9.59879091800106665211e-01, 9.45198953442299649730e-01,
    9.32060075959230460718e-01, 9.19991505039347012840e-01,
    9.08726440052130879366e-01, 8.98095921898343418910e-01,
    8.87984660755833377088e-01, 8.78309655808917399966e-01,
    8.69008688036857046555e-01, 8.60033621196331532488e-01,
    8.51346258458677951353e-01, 8.42915653112204177333e-01,
    8.34716292986883434679e-01, 8.26726833946221373317e-01,
    8.18929191603702366642e-01, 8.11307874312656274185e-01,
    8.03849483170964274059e-01, 7.96542330422958966274e-01,
    7.89376143566024590648e-01, 7.82341832654802504798e-01,
    7.75431304981187174974e-01, 7.68637315798486264740e-01,
    7.61953346836795386565e-01, 7.55373506507096115214e-01,
    7.48892447219156820459e-01, 7.42505296340151055290e-01,
    7.36207598126862650112e-01, 7.29995264561476231435e-01,
    7.23864533468630222401e-01, 7.17811932630721960535e-01,
    7.11834248878248421200e-01, 7.05928501332754310127e-01,
    7.00091918136511615067e-01, 6.94321916126116711609e-01,
    6.88616083004671808432e-01, 6.82972161644994857355e-01,
    6.77388036218773526009e-01, 6.71861719897082099173e-01,
    6.66391343908750100056e-01, 6.60975147776663107813e-01,
    6.55611470579697264149e-01, 6.50298743110816701574e-01,
    6.45035480820822293424e-01, 6.39820277453056585060e-01,
    6.34651799287623608059e-01, 6.29528779924836690007e-01,
    6.24450015547026504592e-01, 6.19414360605834324325e-01,
    6.14420723888913888899e-01, 6.09468064925773433949e-01,
    6.04555390697467776029e-01, 5.99681752619125263415e-01,
    5.94846243767987448159e-01, 5.90047996332826008015e-01,
    5.85286179263371453274e-01, 5.80559996100790898232e-01,
    5.75868682972353718164e-01, 5.71211506735253227163e-01,
    5.66587763256164445025e-01, 5.61996775814524340831e-01,
    5.57437893618765945014e-01, 5.52910490425832290562e-01,
    5.48413963255265812791e-01, 5.43947731190026262382e-01,
    5.39511234256952132426e-01, 5.35103932380457614215e-01,
    5.30725304403662057062e-01, 5.26374847171684479008e-01,
    5.22052074672321841931e-01, 5.17756517229756352272e-01,
    5.13487720747326958914e-01, 5.09245245995747941592e-01,
    5.05028667943468123624e-01, 5.00837575126148681903e-01,
    4.96671569052489714213e-01, 4.92530263643868537748e-01,
    4.88413284705458028423e-01, 4.84320269426683325253e-01,
    4.80250865909046753544e-01, 4.76204732719505863248e-01,
    4.72181538467730199660e-01, 4.68180961405693596422e-01,
    4.64202689048174355069e-01, 4.60246417812842867345e-01,
    4.56311852678716434184e-01, 4.52398706861848520777e-01,
    4.48506701507203064949e-01, 4.44635565395739396077e-01,
    4.40785034665803987508e-01, 4.36954852547985550526e-01,
    4.33144769112652261445e-01, 4.29354541029441427735e-01,
    4.25583931338021970170e-01, 4.21832709229495894654e-01,
    4.18100649837848226120e-01, 4.14387534040891125642e-01,
    4.10693148270188157500e-01, 4.07017284329473372217e-01,
    4.03359739221114510510e-01, 3.99720314980197222177e-01,
    3.96098818515832451492e-01, 3.92495061459315619512e-01,
    3.88908860018788715696e-01, 3.85340034840077283462e-01,
    3.81788410873393657674e-01, 3.78253817245619183840e-01,
    3.74736087137891138443e-01, 3.71235057668239498696e-01,
    3.67750569779032587814e-01, 3.64282468129004055601e-01,
    3.60830600989648031529e-01, 3.57394820145780500731e-01,
    3.53974980800076777232e-01, 3.50570941481406106455e-01,
    3.47182563956793643900e-01, 3.43809713146850715049e-01,
    3.40452257044521866547e-01, 3.37110066637006045021e-01,
    3.33783015830718454708e-01, 3.30470981379163586400e-01,
    3.27173842813601400970e-01, 3.23891482376391093290e-01,
    3.20623784956905355514e-01, 3.17370638029913609834e-01,
    3.14131931596337177215e-01, 3.10907558126286509559e-01,
    3.07697412504292056035e-01, 3.04501391976649993243e-01,
    3.01319396100803049698e-01, 2.98151326696685481377e-01,
    2.94997087799961810184e-01, 2.91856585617095209972e-01,
    2.88729728482182923521e-01, 2.85616426815501756042e-01,
    2.82516593083707578948e-01, 2.79430141761637940157e-01,
    2.76356989295668320494e-01, 2.73297054068577072172e-01,
    2.70250256365875463072e-01, 2.67216518343561471038e-01,
    2.64195763997261190426e-01, 2.61187919132721213522e-01,
    2.58192911337619235290e-01, 2.55210669954661961700e-01,
    2.52241126055942177508e-01, 2.49284212418528522415e-01,
    2.46339863501263828249e-01, 2.43408015422750312329e-01,
    2.40488605940500588254e-01, 2.37581574431238090606e-01,
    2.34686861872330010392e-01, 2.31804410824338724684e-01,
    2.28934165414680340644e-01, 2.26076071322380278694e-01,
    2.23230075763917484855e-01, 2.20396127480151998723e-01,
    2.17574176724331130872e-01, 2.14764175251173583536e-01,
    2.11966076307030182324e-01, 2.09179834621125076977e-01,
    2.06405406397880797353e-01, 2.03642749310334908452e-01,
    2.00891822494656591136e-01, 1.98152586545775138971e-01,
    1.95425003514134304483e-01, 1.92709036903589175926e-01,
    1.90004651670464985713e-01, 1.87311814223800304768e-01,
    1.84630492426799269756e-01, 1.81960655599522513892e-01,
    1.79302274522847582272e-01, 1.76655321443734858455e-01,
    1.74019770081838553999e-01, 1.71395595637505754327e-01,
    1.68782774801211288285e-01, 1.66181285764481906364e-01,
    1.63591108232365584074e-01, 1.61012223437511009516e-01,
    1.58444614155924284882e-01, 1.55888264724479197465e-01,
    1.53343161060262855866e-01, 1.50809290681845675763e-01,
    1.48286642732574552861e-01, 1.45775208005994028060e-01,
    1.43274978973513461566e-01, 1.40785949814444699690e-01,
    1.38308116448550733057e-01, 1.35841476571253755301e-01,
    1.33386029691669155683e-01, 1.30941777173644358090e-01,
    1.28508722279999570981e-01, 1.26086870220185887081e-01,
    1.23676228201596571932e-01, 1.21276805484790306533e-01,
    1.18888613442910059947e-01, 1.16511665625610869035e-01,
    1.14145977827838487895e-01, 1.11791568163838089811e-01,
    1.09448457146811797824e-01, 1.07116667774683801961e-01,
    1.04796225622487068629e-01, 1.02487158941935246892e-01,
    1.00189498768810017482e-01, 9.79032790388624646338e-02,
    9.56285367130089991594e-02, 9.33653119126910124859e-02,
    9.11136480663737591268e-02, 8.88735920682758862021e-02,
    8.66451944505580717859e-02, 8.44285095703534715916e-02,
    8.22235958132029043366e-02, 8.00305158146630696292e-02,
    7.78493367020961224423e-02, 7.56801303589271778804e-02,
    7.35229737139813238622e-02, 7.13779490588904025339e-02,
    6.92451443970067553879e-02, 6.71246538277884968737e-02,
    6.50165779712428976156e-02, 6.29210244377581412456e-02,
    6.08381083495398780614e-02, 5.87679529209337372930e-02,
    5.67106901062029017391e-02, 5.46664613248889208474e-02,
    5.26354182767921896513e-02, 5.06177238609477817000e-02,
    4.86135532158685421122e-02, 4.66230949019303814174e-02,
    4.46465522512944634759e-02, 4.26841449164744590750e-02,
    4.07361106559409394401e-02, 3.88027074045261474722e-02,
    3.68842156885673053135e-02, 3.49809414617161251737e-02,
    3.30932194585785779961e-02, 3.12214171919203004046e-02,
    2.93659397581333588001e-02, 2.75272356696031131329e-02,
    2.57058040085489103443e-02, 2.39022033057958785407e-02,
    2.21170627073088502113e-02, 2.03510962300445102935e-02,
    1.86051212757246224594e-02, 1.68800831525431419000e-02,
    1.51770883079353092332e-02, 1.34974506017398673818e-02,
    1.18427578579078790488e-02, 1.02149714397014590439e-02,
    8.61658276939872638800e-03, 7.05087547137322242369e-03,
    5.52240329925099155545e-03, 4.03797259336302356153e-03,
    2.60907274610215926189e-03, 1.26028593049859797236e-03],
    dtype=np.float32)

ke_double = np.array([
    0x001C5214272497C6, 0x0000000000000000, 0x00137D5BD79C317E,
    0x00186EF58E3F3C10, 0x001A9BB7320EB0AE, 0x001BD127F719447C,
    0x001C951D0F88651A, 0x001D1BFE2D5C3972, 0x001D7E5BD56B18B2,
    0x001DC934DD172C70, 0x001E0409DFAC9DC8, 0x001E337B71D47836,
    0x001E5A8B177CB7A2, 0x001E7B42096F046C, 0x001E970DAF08AE3E,
    0x001EAEF5B14EF09E, 0x001EC3BD07B46556, 0x001ED5F6F08799CE,
    0x001EE614AE6E5688, 0x001EF46ECA361CD0, 0x001F014B76DDD4A4,
    0x001F0CE313A796B6, 0x001F176369F1F77A, 0x001F20F20C452570,
    0x001F29AE1951A874, 0x001F31B18FB95532, 0x001F39125157C106,
    0x001F3FE2EB6E694C, 0x001F463332D788FA, 0x001F4C10BF1D3A0E,
    0x001F51874C5C3322, 0x001F56A109C3ECC0, 0x001F5B66D9099996,
    0x001F5FE08210D08C, 0x001F6414DD445772, 0x001F6809F6859678,
    0x001F6BC52A2B02E6, 0x001F6F4B3D32E4F4, 0x001F72A07190F13A,
    0x001F75C8974D09D6, 0x001F78C71B045CC0, 0x001F7B9F12413FF4,
    0x001F7E5346079F8A, 0x001F80E63BE21138, 0x001F835A3DAD9162,
    0x001F85B16056B912, 0x001F87ED89B24262, 0x001F8A10759374FA,
    0x001F8C1BBA3D39AC, 0x001F8E10CC45D04A, 0x001F8FF102013E16,
    0x001F91BD968358E0, 0x001F9377AC47AFD8, 0x001F95204F8B64DA,
    0x001F96B878633892, 0x001F98410C968892, 0x001F99BAE146BA80,
    0x001F9B26BC697F00, 0x001F9C85561B717A, 0x001F9DD759CFD802,
    0x001F9F1D6761A1CE, 0x001FA058140936C0, 0x001FA187EB3A3338,
    0x001FA2AD6F6BC4FC, 0x001FA3C91ACE0682, 0x001FA4DB5FEE6AA2,
    0x001FA5E4AA4D097C, 0x001FA6E55EE46782, 0x001FA7DDDCA51EC4,
    0x001FA8CE7CE6A874, 0x001FA9B793CE5FEE, 0x001FAA9970ADB858,
    0x001FAB745E588232, 0x001FAC48A3740584, 0x001FAD1682BF9FE8,
    0x001FADDE3B5782C0, 0x001FAEA008F21D6C, 0x001FAF5C2418B07E,
    0x001FB012C25B7A12, 0x001FB0C41681DFF4, 0x001FB17050B6F1FA,
    0x001FB2179EB2963A, 0x001FB2BA2BDFA84A, 0x001FB358217F4E18,
    0x001FB3F1A6C9BE0C, 0x001FB486E10CACD6, 0x001FB517F3C793FC,
    0x001FB5A500C5FDAA, 0x001FB62E2837FE58, 0x001FB6B388C9010A,
    0x001FB7353FB50798, 0x001FB7B368DC7DA8, 0x001FB82E1ED6BA08,
    0x001FB8A57B0347F6, 0x001FB919959A0F74, 0x001FB98A85BA7204,
    0x001FB9F861796F26, 0x001FBA633DEEE286, 0x001FBACB2F41EC16,
    0x001FBB3048B49144, 0x001FBB929CAEA4E2, 0x001FBBF23CC8029E,
    0x001FBC4F39D22994, 0x001FBCA9A3E140D4, 0x001FBD018A548F9E,
    0x001FBD56FBDE729C, 0x001FBDAA068BD66A, 0x001FBDFAB7CB3F40,
    0x001FBE491C7364DE, 0x001FBE9540C9695E, 0x001FBEDF3086B128,
    0x001FBF26F6DE6174, 0x001FBF6C9E828AE2, 0x001FBFB031A904C4,
    0x001FBFF1BA0FFDB0, 0x001FC03141024588, 0x001FC06ECF5B54B2,
    0x001FC0AA6D8B1426, 0x001FC0E42399698A, 0x001FC11BF9298A64,
    0x001FC151F57D1942, 0x001FC1861F770F4A, 0x001FC1B87D9E74B4,
    0x001FC1E91620EA42, 0x001FC217EED505DE, 0x001FC2450D3C83FE,
    0x001FC27076864FC2, 0x001FC29A2F90630E, 0x001FC2C23CE98046,
    0x001FC2E8A2D2C6B4, 0x001FC30D654122EC, 0x001FC33087DE9C0E,
    0x001FC3520E0B7EC6, 0x001FC371FADF66F8, 0x001FC390512A2886,
    0x001FC3AD137497FA, 0x001FC3C844013348, 0x001FC3E1E4CCAB40,
    0x001FC3F9F78E4DA8, 0x001FC4107DB85060, 0x001FC4257877FD68,
    0x001FC438E8B5BFC6, 0x001FC44ACF15112A, 0x001FC45B2BF447E8,
    0x001FC469FF6C4504, 0x001FC477495001B2, 0x001FC483092BFBB8,
    0x001FC48D3E457FF6, 0x001FC495E799D21A, 0x001FC49D03DD30B0,
    0x001FC4A29179B432, 0x001FC4A68E8E07FC, 0x001FC4A8F8EBFB8C,
    0x001FC4A9CE16EA9E, 0x001FC4A90B41FA34, 0x001FC4A6AD4E28A0,
    0x001FC4A2B0C82E74, 0x001FC49D11E62DE2, 0x001FC495CC852DF4,
    0x001FC48CDC265EC0, 0x001FC4823BEC237A, 0x001FC475E696DEE6,
    0x001FC467D6817E82, 0x001FC458059DC036, 0x001FC4466D702E20,
    0x001FC433070BCB98, 0x001FC41DCB0D6E0E, 0x001FC406B196BBF6,
    0x001FC3EDB248CB62, 0x001FC3D2C43E593C, 0x001FC3B5DE0591B4,
    0x001FC396F599614C, 0x001FC376005A4592, 0x001FC352F3069370,
    0x001FC32DC1B22818, 0x001FC3065FBD7888, 0x001FC2DCBFCBF262,
    0x001FC2B0D3B99F9E, 0x001FC2828C8FFCF0, 0x001FC251DA79F164,
    0x001FC21EACB6D39E, 0x001FC1E8F18C6756, 0x001FC1B09637BB3C,
    0x001FC17586DCCD10, 0x001FC137AE74D6B6, 0x001FC0F6F6BB2414,
    0x001FC0B348184DA4, 0x001FC06C898BAFF0, 0x001FC022A092F364,
    0x001FBFD5710F72B8, 0x001FBF84DD29488E, 0x001FBF30C52FC60A,
    0x001FBED907770CC6, 0x001FBE7D80327DDA, 0x001FBE1E094BA614,
    0x001FBDBA7A354408, 0x001FBD52A7B9F826, 0x001FBCE663C6201A,
    0x001FBC757D2C4DE4, 0x001FBBFFBF63B7AA, 0x001FBB84F23FE6A2,
    0x001FBB04D9A0D18C, 0x001FBA7F351A70AC, 0x001FB9F3BF92B618,
    0x001FB9622ED4ABFC, 0x001FB8CA33174A16, 0x001FB82B76765B54,
    0x001FB7859C5B895C, 0x001FB6D840D55594, 0x001FB622F7D96942,
    0x001FB5654C6F37E0, 0x001FB49EBFBF69D2, 0x001FB3CEC803E746,
    0x001FB2F4CF539C3E, 0x001FB21032442852, 0x001FB1203E5A9604,
    0x001FB0243042E1C2, 0x001FAF1B31C479A6, 0x001FAE045767E104,
    0x001FACDE9DBF2D72, 0x001FABA8E640060A, 0x001FAA61F399FF28,
    0x001FA908656F66A2, 0x001FA79AB3508D3C, 0x001FA61726D1F214,
    0x001FA47BD48BEA00, 0x001FA2C693C5C094, 0x001FA0F4F47DF314,
    0x001F9F04336BBE0A, 0x001F9CF12B79F9BC, 0x001F9AB84415ABC4,
    0x001F98555B782FB8, 0x001F95C3ABD03F78, 0x001F92FDA9CEF1F2,
    0x001F8FFCDA9AE41C, 0x001F8CB99E7385F8, 0x001F892AEC479606,
    0x001F8545F904DB8E, 0x001F80FDC336039A, 0x001F7C427839E926,
    0x001F7700A3582ACC, 0x001F71200F1A241C, 0x001F6A8234B7352A,
    0x001F630000A8E266, 0x001F5A66904FE3C4, 0x001F50724ECE1172,
    0x001F44C7665C6FDA, 0x001F36E5A38A59A2, 0x001F26143450340A,
    0x001F113E047B0414, 0x001EF6AEFA57CBE6, 0x001ED38CA188151E,
    0x001EA2A61E122DB0, 0x001E5961C78B267C, 0x001DDDF62BAC0BB0,
    0x001CDB4DD9E4E8C0], dtype=np.uint64)

we_double = np.array([
    9.655740063209182975e-16, 7.089014243955414331e-18,
    1.163941249669122378e-17, 1.524391512353216015e-17,
    1.833284885723743916e-17, 2.108965109464486630e-17,
    2.361128077843138196e-17, 2.595595772310893952e-17,
    2.816173554197752338e-17, 3.025504130321382330e-17,
    3.225508254836375280e-17, 3.417632340185027033e-17,
    3.602996978734452488e-17, 3.782490776869649048e-17,
    3.956832198097553231e-17, 4.126611778175946428e-17,
    4.292321808442525631e-17, 4.454377743282371417e-17,
    4.613133981483185932e-17, 4.768895725264635940e-17,
    4.921928043727962847e-17, 5.072462904503147014e-17,
    5.220704702792671737e-17, 5.366834661718192181e-17,
    5.511014372835094717e-17, 5.653388673239667134e-17,
    5.794088004852766616e-17, 5.933230365208943081e-17,
    6.070922932847179572e-17, 6.207263431163193485e-17,
    6.342341280303076511e-17, 6.476238575956142121e-17,
    6.609030925769405241e-17, 6.740788167872722244e-17,
    6.871574991183812442e-17, 7.001451473403929616e-17,
    7.130473549660643409e-17, 7.258693422414648352e-17,
    7.386159921381791997e-17, 7.512918820723728089e-17,
    7.639013119550825792e-17, 7.764483290797848102e-17,
    7.889367502729790548e-17, 8.013701816675454434e-17,
    8.137520364041762206e-17, 8.260855505210038174e-17,
    8.383737972539139383e-17, 8.506196999385323132e-17,
    8.628260436784112996e-17, 8.749954859216182511e-17,
    8.871305660690252281e-17, 8.992337142215357066e-17,
    9.113072591597909173e-17, 9.233534356381788123e-17,
    9.353743910649128938e-17, 9.473721916312949566e-17,
    9.593488279457997317e-17, 9.713062202221521206e-17,
    9.832462230649511362e-17, 9.951706298915071878e-17,
    1.007081177024294931e-16, 1.018979547484694078e-16,
    1.030867374515421954e-16, 1.042746244856188556e-16,
    1.054617701794576406e-16, 1.066483248011914702e-16,
    1.078344348241948498e-16, 1.090202431758350473e-16,
    1.102058894705578110e-16, 1.113915102286197502e-16,
    1.125772390816567488e-16, 1.137632069661684705e-16,
    1.149495423059009298e-16, 1.161363711840218308e-16,
    1.173238175059045788e-16, 1.185120031532669434e-16,
    1.197010481303465158e-16, 1.208910707027385520e-16,
    1.220821875294706151e-16, 1.232745137888415193e-16,
    1.244681632985112523e-16, 1.256632486302898513e-16,
    1.268598812200397542e-16, 1.280581714730749379e-16,
    1.292582288654119552e-16, 1.304601620412028847e-16,
    1.316640789066572582e-16, 1.328700867207380889e-16,
    1.340782921828999433e-16, 1.352888015181175458e-16,
    1.365017205594397770e-16, 1.377171548282880964e-16,
    1.389352096127063919e-16, 1.401559900437571538e-16,
    1.413796011702485188e-16, 1.426061480319665444e-16,
    1.438357357315790180e-16, 1.450684695053687684e-16,
    1.463044547929475721e-16, 1.475437973060951633e-16,
    1.487866030968626066e-16, 1.500329786250736949e-16,
    1.512830308253539427e-16, 1.525368671738125550e-16,
    1.537945957544996933e-16, 1.550563253257577148e-16,
    1.563221653865837505e-16, 1.575922262431176140e-16,
    1.588666190753684151e-16, 1.601454560042916733e-16,
    1.614288501593278662e-16, 1.627169157465130500e-16,
    1.640097681172717950e-16, 1.653075238380036909e-16,
    1.666103007605742067e-16, 1.679182180938228863e-16,
    1.692313964762022267e-16, 1.705499580496629830e-16,
    1.718740265349031656e-16, 1.732037273081008369e-16,
    1.745391874792533975e-16, 1.758805359722491379e-16,
    1.772279036068006489e-16, 1.785814231823732619e-16,
    1.799412295642463721e-16, 1.813074597718501559e-16,
    1.826802530695252266e-16, 1.840597510598587828e-16,
    1.854460977797569461e-16, 1.868394397994192684e-16,
    1.882399263243892051e-16, 1.896477093008616722e-16,
    1.910629435244376536e-16, 1.924857867525243818e-16,
    1.939163998205899420e-16, 1.953549467624909132e-16,
    1.968015949351037382e-16, 1.982565151475019047e-16,
    1.997198817949342081e-16, 2.011918729978734671e-16,
    2.026726707464198289e-16, 2.041624610503588774e-16,
    2.056614340951917875e-16, 2.071697844044737034e-16,
    2.086877110088159721e-16, 2.102154176219292789e-16,
    2.117531128241075913e-16, 2.133010102535779087e-16,
    2.148593288061663316e-16, 2.164282928437604723e-16,
    2.180081324120784027e-16, 2.195990834682870728e-16,
    2.212013881190495942e-16, 2.228152948696180545e-16,
    2.244410588846308588e-16, 2.260789422613173739e-16,
    2.277292143158621037e-16, 2.293921518837311354e-16,
    2.310680396348213318e-16, 2.327571704043534613e-16,
    2.344598455404957859e-16, 2.361763752697773994e-16,
    2.379070790814276700e-16, 2.396522861318623520e-16,
    2.414123356706293277e-16, 2.431875774892255956e-16,
    2.449783723943070217e-16, 2.467850927069288738e-16,
    2.486081227895851719e-16, 2.504478596029557040e-16,
    2.523047132944217013e-16, 2.541791078205812227e-16,
    2.560714816061770759e-16, 2.579822882420530896e-16,
    2.599119972249746917e-16, 2.618610947423924219e-16,
    2.638300845054942823e-16, 2.658194886341845120e-16,
    2.678298485979525166e-16, 2.698617262169488933e-16,
    2.719157047279818500e-16, 2.739923899205814823e-16,
    2.760924113487617126e-16, 2.782164236246436081e-16,
    2.803651078006983464e-16, 2.825391728480253184e-16,
    2.847393572388174091e-16, 2.869664306419817679e-16,
    2.892211957417995598e-16, 2.915044901905293183e-16,
    2.938171887070028633e-16, 2.961602053345465687e-16,
    2.985344958730045276e-16, 3.009410605012618141e-16,
    3.033809466085003416e-16, 3.058552518544860874e-16,
    3.083651274815310004e-16, 3.109117819034266344e-16,
    3.134964845996663118e-16, 3.161205703467105734e-16,
    3.187854438219713117e-16, 3.214925846206797361e-16,
    3.242435527309451638e-16, 3.270399945182240440e-16,
    3.298836492772283149e-16, 3.327763564171671408e-16,
    3.357200633553244075e-16, 3.387168342045505162e-16,
    3.417688593525636996e-16, 3.448784660453423890e-16,
    3.480481301037442286e-16, 3.512804889222979418e-16,
    3.545783559224791863e-16, 3.579447366604276541e-16,
    3.613828468219060593e-16, 3.648961323764542545e-16,
    3.684882922095621322e-16, 3.721633036080207290e-16,
    3.759254510416256532e-16, 3.797793587668874387e-16,
    3.837300278789213687e-16, 3.877828785607895292e-16,
    3.919437984311428867e-16, 3.962191980786774996e-16,
    4.006160751056541688e-16, 4.051420882956573177e-16,
    4.098056438903062509e-16, 4.146159964290904582e-16,
    4.195833672073398926e-16, 4.247190841824385048e-16,
    4.300357481667470702e-16, 4.355474314693952008e-16,
    4.412699169036069903e-16, 4.472209874259932285e-16,
    4.534207798565834480e-16, 4.598922204905932469e-16,
    4.666615664711475780e-16, 4.737590853262492027e-16,
    4.812199172829237933e-16, 4.890851827392209900e-16,
    4.974034236191939753e-16, 5.062325072144159699e-16,
    5.156421828878082953e-16, 5.257175802022274839e-16,
    5.365640977112021618e-16, 5.483144034258703912e-16,
    5.611387454675159622e-16, 5.752606481503331688e-16,
    5.909817641652102998e-16, 6.087231416180907671e-16,
    6.290979034877557049e-16, 6.530492053564040799e-16,
    6.821393079028928626e-16, 7.192444966089361564e-16,
    7.706095350032096755e-16, 8.545517038584027421e-16],
    dtype=np.float64)

fe_double = np.array([
    1.000000000000000000e+00, 9.381436808621747003e-01,
    9.004699299257464817e-01, 8.717043323812035949e-01,
    8.477855006239896074e-01, 8.269932966430503241e-01,
    8.084216515230083777e-01, 7.915276369724956185e-01,
    7.759568520401155522e-01, 7.614633888498962833e-01,
    7.478686219851951034e-01, 7.350380924314234843e-01,
    7.228676595935720206e-01, 7.112747608050760117e-01,
    7.001926550827881623e-01, 6.895664961170779872e-01,
    6.793505722647653622e-01, 6.695063167319247333e-01,
    6.600008410789997004e-01, 6.508058334145710999e-01,
    6.418967164272660897e-01, 6.332519942143660652e-01,
    6.248527387036659775e-01, 6.166821809152076561e-01,
    6.087253820796220127e-01, 6.009689663652322267e-01,
    5.934009016917334289e-01, 5.860103184772680329e-01,
    5.787873586028450257e-01, 5.717230486648258170e-01,
    5.648091929124001709e-01, 5.580382822625874484e-01,
    5.514034165406412891e-01, 5.448982376724396115e-01,
    5.385168720028619127e-01, 5.322538802630433219e-01,
    5.261042139836197284e-01, 5.200631773682335979e-01,
    5.141263938147485613e-01, 5.082897764106428795e-01,
    5.025495018413477233e-01, 4.969019872415495476e-01,
    4.913438695940325340e-01, 4.858719873418849144e-01,
    4.804833639304542103e-01, 4.751751930373773747e-01,
    4.699448252839599771e-01, 4.647897562504261781e-01,
    4.597076156421376902e-01, 4.546961574746155033e-01,
    4.497532511627549967e-01, 4.448768734145485126e-01,
    4.400651008423538957e-01, 4.353161032156365740e-01,
    4.306281372884588343e-01, 4.259995411430343437e-01,
    4.214287289976165751e-01, 4.169141864330028757e-01,
    4.124544659971611793e-01, 4.080481831520323954e-01,
    4.036940125305302773e-01, 3.993906844752310725e-01,
    3.951369818332901573e-01, 3.909317369847971069e-01,
    3.867738290841376547e-01, 3.826621814960098344e-01,
    3.785957594095807899e-01, 3.745735676159021588e-01,
    3.705946484351460013e-01, 3.666580797815141568e-01,
    3.627629733548177748e-01, 3.589084729487497794e-01,
    3.550937528667874599e-01, 3.513180164374833381e-01,
    3.475804946216369817e-01, 3.438804447045024082e-01,
    3.402171490667800224e-01, 3.365899140286776059e-01,
    3.329980687618089852e-01, 3.294409642641363267e-01,
    3.259179723935561879e-01, 3.224284849560891675e-01,
    3.189719128449572394e-01, 3.155476852271289490e-01,
    3.121552487741795501e-01, 3.087940669345601852e-01,
    3.054636192445902565e-01, 3.021634006756935276e-01,
    2.988929210155817917e-01, 2.956517042812611962e-01,
    2.924392881618925744e-01, 2.892552234896777485e-01,
    2.860990737370768255e-01, 2.829704145387807457e-01,
    2.798688332369729248e-01, 2.767939284485173568e-01,
    2.737453096528029706e-01, 2.707225967990600224e-01,
    2.677254199320447947e-01, 2.647534188350622042e-01,
    2.618062426893629779e-01, 2.588835497490162285e-01,
    2.559850070304153791e-01, 2.531102900156294577e-01,
    2.502590823688622956e-01, 2.474310756653276266e-01,
    2.446259691318921070e-01, 2.418434693988772144e-01,
    2.390832902624491774e-01, 2.363451524570596429e-01,
    2.336287834374333461e-01, 2.309339171696274118e-01,
    2.282602939307167011e-01, 2.256076601166840667e-01,
    2.229757680581201940e-01, 2.203643758433594946e-01,
    2.177732471487005272e-01, 2.152021510753786837e-01,
    2.126508619929782795e-01, 2.101191593889882581e-01,
    2.076068277242220372e-01, 2.051136562938377095e-01,
    2.026394390937090173e-01, 2.001839746919112650e-01,
    1.977470661050988732e-01, 1.953285206795632167e-01,
    1.929281499767713515e-01, 1.905457696631953912e-01,
    1.881811994042543179e-01, 1.858342627621971110e-01,
    1.835047870977674633e-01, 1.811926034754962889e-01,
    1.788975465724783054e-01, 1.766194545904948843e-01,
    1.743581691713534942e-01, 1.721135353153200598e-01,
    1.698854013025276610e-01, 1.676736186172501919e-01,
    1.654780418749360049e-01, 1.632985287519018169e-01,
    1.611349399175920349e-01, 1.589871389693142123e-01,
    1.568549923693652315e-01, 1.547383693844680830e-01,
    1.526371420274428570e-01, 1.505511850010398944e-01,
    1.484803756438667910e-01, 1.464245938783449441e-01,
    1.443837221606347754e-01, 1.423576454324722018e-01,
    1.403462510748624548e-01, 1.383494288635802039e-01,
    1.363670709264288572e-01, 1.343990717022136294e-01,
    1.324453279013875218e-01, 1.305057384683307731e-01,
    1.285802045452281717e-01, 1.266686294375106714e-01,
    1.247709185808309612e-01, 1.228869795095451356e-01,
    1.210167218266748335e-01, 1.191600571753276827e-01,
    1.173168992115555670e-01, 1.154871635786335338e-01,
    1.136707678827443141e-01, 1.118676316700562973e-01,
    1.100776764051853845e-01, 1.083008254510337970e-01,
    1.065370040500016602e-01, 1.047861393065701724e-01,
    1.030481601712577161e-01, 1.013229974259536315e-01,
    9.961058367063713170e-02, 9.791085331149219917e-02,
    9.622374255043279756e-02, 9.454918937605585882e-02,
    9.288713355604354127e-02, 9.123751663104015530e-02,
    8.960028191003285847e-02, 8.797537446727021759e-02,
    8.636274114075691288e-02, 8.476233053236811865e-02,
    8.317409300963238272e-02, 8.159798070923741931e-02,
    8.003394754231990538e-02, 7.848194920160642130e-02,
    7.694194317048050347e-02, 7.541388873405840965e-02,
    7.389774699236474620e-02, 7.239348087570873780e-02,
    7.090105516237182881e-02, 6.942043649872875477e-02,
    6.795159342193660135e-02, 6.649449638533977414e-02,
    6.504911778675374900e-02, 6.361543199980733421e-02,
    6.219341540854099459e-02, 6.078304644547963265e-02,
    5.938430563342026597e-02, 5.799717563120065922e-02,
    5.662164128374287675e-02, 5.525768967669703741e-02,
    5.390531019604608703e-02, 5.256449459307169225e-02,
    5.123523705512628146e-02, 4.991753428270637172e-02,
    4.861138557337949667e-02, 4.731679291318154762e-02,
    4.603376107617516977e-02, 4.476229773294328196e-02,
    4.350241356888818328e-02, 4.225412241331623353e-02,
    4.101744138041481941e-02, 3.979239102337412542e-02,
    3.857899550307485742e-02, 3.737728277295936097e-02,
    3.618728478193142251e-02, 3.500903769739741045e-02,
    3.384258215087432992e-02, 3.268796350895953468e-02,
    3.154523217289360859e-02, 3.041444391046660423e-02,
    2.929566022463739317e-02, 2.818894876397863569e-02,
    2.709438378095579969e-02, 2.601204664513421735e-02,
    2.494202641973178314e-02, 2.388442051155817078e-02,
    2.283933540638524023e-02, 2.180688750428358066e-02,
    2.078720407257811723e-02, 1.978042433800974303e-02,
    1.878670074469603046e-02, 1.780620041091136169e-02,
    1.683910682603994777e-02, 1.588562183997316302e-02,
    1.494596801169114850e-02, 1.402039140318193759e-02,
    1.310916493125499106e-02, 1.221259242625538123e-02,
    1.133101359783459695e-02, 1.046481018102997894e-02,
    9.614413642502209895e-03, 8.780314985808975251e-03,
    7.963077438017040002e-03, 7.163353183634983863e-03,
    6.381905937319179087e-03, 5.619642207205483020e-03,
    4.877655983542392333e-03, 4.157295120833795314e-03,
    3.460264777836904049e-03, 2.788798793574076128e-03,
    2.145967743718906265e-03, 1.536299780301572356e-03,
    9.672692823271745359e-04, 4.541343538414967652e-04],
    dtype=np.float64)

ke_float = np.array([
    0x00714851, 0x00000000, 0x004DF56F, 0x0061BBD6, 0x006A6EDD,
    0x006F44A0, 0x00725474, 0x00746FF9, 0x0075F96F, 0x007724D3,
    0x00781027, 0x0078CDEE, 0x00796A2C, 0x0079ED08, 0x007A5C37,
    0x007ABBD7, 0x007B0EF4, 0x007B57DC, 0x007B9853, 0x007BD1BB,
    0x007C052E, 0x007C338C, 0x007C5D8E, 0x007C83C8, 0x007CA6B8,
    0x007CC6C6, 0x007CE449, 0x007CFF8C, 0x007D18CD, 0x007D3043,
    0x007D461D, 0x007D5A84, 0x007D6D9B, 0x007D7F82, 0x007D9053,
    0x007DA028, 0x007DAF15, 0x007DBD2D, 0x007DCA82, 0x007DD722,
    0x007DE31C, 0x007DEE7C, 0x007DF94D, 0x007E0399, 0x007E0D69,
    0x007E16C6, 0x007E1FB6, 0x007E2842, 0x007E306F, 0x007E3843,
    0x007E3FC4, 0x007E46F6, 0x007E4DDF, 0x007E5481, 0x007E5AE2,
    0x007E6104, 0x007E66EC, 0x007E6C9B, 0x007E7215, 0x007E775D,
    0x007E7C76, 0x007E8160, 0x007E8620, 0x007E8AB6, 0x007E8F24,
    0x007E936D, 0x007E9793, 0x007E9B95, 0x007E9F77, 0x007EA33A,
    0x007EA6DE, 0x007EAA66, 0x007EADD1, 0x007EB123, 0x007EB45A,
    0x007EB779, 0x007EBA80, 0x007EBD71, 0x007EC04B, 0x007EC310,
    0x007EC5C1, 0x007EC85E, 0x007ECAE9, 0x007ECD61, 0x007ECFC7,
    0x007ED21C, 0x007ED460, 0x007ED694, 0x007ED8B9, 0x007EDACE,
    0x007EDCD5, 0x007EDECE, 0x007EE0B8, 0x007EE296, 0x007EE466,
    0x007EE62A, 0x007EE7E2, 0x007EE98D, 0x007EEB2D, 0x007EECC1,
    0x007EEE4A, 0x007EEFC9, 0x007EF13D, 0x007EF2A7, 0x007EF406,
    0x007EF55C, 0x007EF6A8, 0x007EF7EB, 0x007EF924, 0x007EFA55,
    0x007EFB7D, 0x007EFC9C, 0x007EFDB2, 0x007EFEC1, 0x007EFFC7,
    0x007F00C5, 0x007F01BB, 0x007F02AA, 0x007F0391, 0x007F0470,
    0x007F0548, 0x007F0618, 0x007F06E2, 0x007F07A4, 0x007F0860,
    0x007F0914, 0x007F09C2, 0x007F0A69, 0x007F0B09, 0x007F0BA3,
    0x007F0C36, 0x007F0CC2, 0x007F0D48, 0x007F0DC8, 0x007F0E41,
    0x007F0EB4, 0x007F0F21, 0x007F0F88, 0x007F0FE8, 0x007F1042,
    0x007F1096, 0x007F10E4, 0x007F112B, 0x007F116D, 0x007F11A8,
    0x007F11DD, 0x007F120C, 0x007F1235, 0x007F1258, 0x007F1274,
    0x007F128A, 0x007F129A, 0x007F12A4, 0x007F12A7, 0x007F12A4,
    0x007F129B, 0x007F128B, 0x007F1274, 0x007F1257, 0x007F1233,
    0x007F1209, 0x007F11D8, 0x007F119F, 0x007F1160, 0x007F111A,
    0x007F10CC, 0x007F1077, 0x007F101B, 0x007F0FB7, 0x007F0F4B,
    0x007F0ED7, 0x007F0E5C, 0x007F0DD8, 0x007F0D4C, 0x007F0CB7,
    0x007F0C19, 0x007F0B73, 0x007F0AC3, 0x007F0A0A, 0x007F0947,
    0x007F087B, 0x007F07A4, 0x007F06C2, 0x007F05D6, 0x007F04DF,
    0x007F03DC, 0x007F02CD, 0x007F01B2, 0x007F008B, 0x007EFF56,
    0x007EFE13, 0x007EFCC3, 0x007EFB64, 0x007EF9F6, 0x007EF878,
    0x007EF6EA, 0x007EF54B, 0x007EF39A, 0x007EF1D6, 0x007EEFFF,
    0x007EEE14, 0x007EEC13, 0x007EE9FD, 0x007EE7CF, 0x007EE589,
    0x007EE329, 0x007EE0AE, 0x007EDE16, 0x007EDB61, 0x007ED88C,
    0x007ED595, 0x007ED27B, 0x007ECF3B, 0x007ECBD3, 0x007EC841,
    0x007EC481, 0x007EC091, 0x007EBC6D, 0x007EB811, 0x007EB37A,
    0x007EAEA4, 0x007EA988, 0x007EA422, 0x007E9E6B, 0x007E985D,
    0x007E91EF, 0x007E8B1A, 0x007E83D4, 0x007E7C11, 0x007E73C5,
    0x007E6AE1, 0x007E6155, 0x007E570F, 0x007E4BF7, 0x007E3FF3,
    0x007E32E6, 0x007E24AC, 0x007E1518, 0x007E03F7, 0x007DF10A,
    0x007DDC03, 0x007DC480, 0x007DAA09, 0x007D8C00, 0x007D699A,
    0x007D41C9, 0x007D131E, 0x007CDB97, 0x007C9851, 0x007C44F8,
    0x007BDABC, 0x007B4E33, 0x007A8A98, 0x00796587, 0x007777D9,
    0x00736D37, ], dtype=np.uint32)

we_float = np.array([
    1.03677719e-06, 7.61177108e-09, 1.24977240e-08, 1.63680292e-08,
    1.96847466e-08, 2.26448404e-08, 2.53524197e-08, 2.78699974e-08,
    3.02384333e-08, 3.24861032e-08, 3.46336312e-08, 3.66965478e-08,
    3.86868855e-08, 4.06141855e-08, 4.24861622e-08, 4.43091566e-08,
    4.60884545e-08, 4.78285168e-08, 4.95331490e-08, 5.12056279e-08,
    5.28488000e-08, 5.44651557e-08, 5.60568899e-08, 5.76259484e-08,
    5.91740662e-08, 6.07027987e-08, 6.22135462e-08, 6.37075759e-08,
    6.51860386e-08, 6.66499836e-08, 6.81003709e-08, 6.95380822e-08,
    7.09639292e-08, 7.23786618e-08, 7.37829746e-08, 7.51775128e-08,
    7.65628768e-08, 7.79396272e-08, 7.93082883e-08, 8.06693516e-08,
    8.20232788e-08, 8.33705045e-08, 8.47114385e-08, 8.60464681e-08,
    8.73759596e-08, 8.87002606e-08, 9.00197010e-08, 9.13345948e-08,
    9.26452410e-08, 9.39519249e-08, 9.52549192e-08, 9.65544849e-08,
    9.78508719e-08, 9.91443202e-08, 1.00435060e-07, 1.01723315e-07,
    1.03009296e-07, 1.04293211e-07, 1.05575259e-07, 1.06855633e-07,
    1.08134518e-07, 1.09412096e-07, 1.10688542e-07, 1.11964025e-07,
    1.13238713e-07, 1.14512767e-07, 1.15786343e-07, 1.17059595e-07,
    1.18332673e-07, 1.19605723e-07, 1.20878890e-07, 1.22152313e-07,
    1.23426131e-07, 1.24700479e-07, 1.25975490e-07, 1.27251294e-07,
    1.28528022e-07, 1.29805799e-07, 1.31084751e-07, 1.32365001e-07,
    1.33646673e-07, 1.34929886e-07, 1.36214760e-07, 1.37501415e-07,
    1.38789966e-07, 1.40080532e-07, 1.41373228e-07, 1.42668169e-07,
    1.43965470e-07, 1.45265245e-07, 1.46567606e-07, 1.47872669e-07,
    1.49180545e-07, 1.50491348e-07, 1.51805191e-07, 1.53122186e-07,
    1.54442445e-07, 1.55766083e-07, 1.57093212e-07, 1.58423946e-07,
    1.59758399e-07, 1.61096684e-07, 1.62438917e-07, 1.63785214e-07,
    1.65135690e-07, 1.66490462e-07, 1.67849647e-07, 1.69213364e-07,
    1.70581733e-07, 1.71954874e-07, 1.73332908e-07, 1.74715958e-07,
    1.76104148e-07, 1.77497602e-07, 1.78896448e-07, 1.80300814e-07,
    1.81710828e-07, 1.83126623e-07, 1.84548331e-07, 1.85976086e-07,
    1.87410026e-07, 1.88850288e-07, 1.90297012e-07, 1.91750343e-07,
    1.93210424e-07, 1.94677403e-07, 1.96151428e-07, 1.97632653e-07,
    1.99121231e-07, 2.00617321e-07, 2.02121082e-07, 2.03632677e-07,
    2.05152273e-07, 2.06680040e-07, 2.08216149e-07, 2.09760777e-07,
    2.11314104e-07, 2.12876312e-07, 2.14447590e-07, 2.16028129e-07,
    2.17618123e-07, 2.19217773e-07, 2.20827283e-07, 2.22446862e-07,
    2.24076723e-07, 2.25717086e-07, 2.27368174e-07, 2.29030216e-07,
    2.30703448e-07, 2.32388110e-07, 2.34084450e-07, 2.35792720e-07,
    2.37513182e-07, 2.39246101e-07, 2.40991752e-07, 2.42750416e-07,
    2.44522382e-07, 2.46307948e-07, 2.48107418e-07, 2.49921109e-07,
    2.51749342e-07, 2.53592452e-07, 2.55450781e-07, 2.57324683e-07,
    2.59214522e-07, 2.61120673e-07, 2.63043524e-07, 2.64983476e-07,
    2.66940939e-07, 2.68916342e-07, 2.70910123e-07, 2.72922739e-07,
    2.74954660e-07, 2.77006373e-07, 2.79078382e-07, 2.81171210e-07,
    2.83285396e-07, 2.85421503e-07, 2.87580110e-07, 2.89761822e-07,
    2.91967265e-07, 2.94197089e-07, 2.96451969e-07, 2.98732610e-07,
    3.01039742e-07, 3.03374127e-07, 3.05736557e-07, 3.08127859e-07,
    3.10548894e-07, 3.13000563e-07, 3.15483804e-07, 3.17999599e-07,
    3.20548974e-07, 3.23133003e-07, 3.25752811e-07, 3.28409576e-07,
    3.31104534e-07, 3.33838984e-07, 3.36614287e-07, 3.39431878e-07,
    3.42293264e-07, 3.45200034e-07, 3.48153864e-07, 3.51156520e-07,
    3.54209871e-07, 3.57315892e-07, 3.60476673e-07, 3.63694431e-07,
    3.66971518e-07, 3.70310433e-07, 3.73713834e-07, 3.77184553e-07,
    3.80725611e-07, 3.84340234e-07, 3.88031877e-07, 3.91804239e-07,
    3.95661291e-07, 3.99607304e-07, 4.03646879e-07, 4.07784981e-07,
    4.12026980e-07, 4.16378695e-07, 4.20846449e-07, 4.25437124e-07,
    4.30158235e-07, 4.35018005e-07, 4.40025460e-07, 4.45190536e-07,
    4.50524210e-07, 4.56038644e-07, 4.61747369e-07, 4.67665494e-07,
    4.73809965e-07, 4.80199879e-07, 4.86856855e-07, 4.93805512e-07,
    5.01074042e-07, 5.08694944e-07, 5.16705952e-07, 5.25151216e-07,
    5.34082859e-07, 5.43563016e-07, 5.53666578e-07, 5.64484953e-07,
    5.76131313e-07, 5.88748108e-07, 6.02518140e-07, 6.17681418e-07,
    6.34561837e-07, 6.53611496e-07, 6.75488730e-07, 7.01206245e-07,
    7.32441505e-07, 7.72282898e-07, 8.27435688e-07, 9.17567905e-07,]
    , dtype=np.float32)

fe_float = np.array([
    1.00000000e+00, 9.38143681e-01, 9.00469930e-01, 8.71704332e-01,
    8.47785501e-01, 8.26993297e-01, 8.08421652e-01, 7.91527637e-01,
    7.75956852e-01, 7.61463389e-01, 7.47868622e-01, 7.35038092e-01,
    7.22867660e-01, 7.11274761e-01, 7.00192655e-01, 6.89566496e-01,
    6.79350572e-01, 6.69506317e-01, 6.60000841e-01, 6.50805833e-01,
    6.41896716e-01, 6.33251994e-01, 6.24852739e-01, 6.16682181e-01,
    6.08725382e-01, 6.00968966e-01, 5.93400902e-01, 5.86010318e-01,
    5.78787359e-01, 5.71723049e-01, 5.64809193e-01, 5.58038282e-01,
    5.51403417e-01, 5.44898238e-01, 5.38516872e-01, 5.32253880e-01,
    5.26104214e-01, 5.20063177e-01, 5.14126394e-01, 5.08289776e-01,
    5.02549502e-01, 4.96901987e-01, 4.91343870e-01, 4.85871987e-01,
    4.80483364e-01, 4.75175193e-01, 4.69944825e-01, 4.64789756e-01,
    4.59707616e-01, 4.54696157e-01, 4.49753251e-01, 4.44876873e-01,
    4.40065101e-01, 4.35316103e-01, 4.30628137e-01, 4.25999541e-01,
    4.21428729e-01, 4.16914186e-01, 4.12454466e-01, 4.08048183e-01,
    4.03694013e-01, 3.99390684e-01, 3.95136982e-01, 3.90931737e-01,
    3.86773829e-01, 3.82662181e-01, 3.78595759e-01, 3.74573568e-01,
    3.70594648e-01, 3.66658080e-01, 3.62762973e-01, 3.58908473e-01,
    3.55093753e-01, 3.51318016e-01, 3.47580495e-01, 3.43880445e-01,
    3.40217149e-01, 3.36589914e-01, 3.32998069e-01, 3.29440964e-01,
    3.25917972e-01, 3.22428485e-01, 3.18971913e-01, 3.15547685e-01,
    3.12155249e-01, 3.08794067e-01, 3.05463619e-01, 3.02163401e-01,
    2.98892921e-01, 2.95651704e-01, 2.92439288e-01, 2.89255223e-01,
    2.86099074e-01, 2.82970415e-01, 2.79868833e-01, 2.76793928e-01,
    2.73745310e-01, 2.70722597e-01, 2.67725420e-01, 2.64753419e-01,
    2.61806243e-01, 2.58883550e-01, 2.55985007e-01, 2.53110290e-01,
    2.50259082e-01, 2.47431076e-01, 2.44625969e-01, 2.41843469e-01,
    2.39083290e-01, 2.36345152e-01, 2.33628783e-01, 2.30933917e-01,
    2.28260294e-01, 2.25607660e-01, 2.22975768e-01, 2.20364376e-01,
    2.17773247e-01, 2.15202151e-01, 2.12650862e-01, 2.10119159e-01,
    2.07606828e-01, 2.05113656e-01, 2.02639439e-01, 2.00183975e-01,
    1.97747066e-01, 1.95328521e-01, 1.92928150e-01, 1.90545770e-01,
    1.88181199e-01, 1.85834263e-01, 1.83504787e-01, 1.81192603e-01,
    1.78897547e-01, 1.76619455e-01, 1.74358169e-01, 1.72113535e-01,
    1.69885401e-01, 1.67673619e-01, 1.65478042e-01, 1.63298529e-01,
    1.61134940e-01, 1.58987139e-01, 1.56854992e-01, 1.54738369e-01,
    1.52637142e-01, 1.50551185e-01, 1.48480376e-01, 1.46424594e-01,
    1.44383722e-01, 1.42357645e-01, 1.40346251e-01, 1.38349429e-01,
    1.36367071e-01, 1.34399072e-01, 1.32445328e-01, 1.30505738e-01,
    1.28580205e-01, 1.26668629e-01, 1.24770919e-01, 1.22886980e-01,
    1.21016722e-01, 1.19160057e-01, 1.17316899e-01, 1.15487164e-01,
    1.13670768e-01, 1.11867632e-01, 1.10077676e-01, 1.08300825e-01,
    1.06537004e-01, 1.04786139e-01, 1.03048160e-01, 1.01322997e-01,
    9.96105837e-02, 9.79108533e-02, 9.62237426e-02, 9.45491894e-02,
    9.28871336e-02, 9.12375166e-02, 8.96002819e-02, 8.79753745e-02,
    8.63627411e-02, 8.47623305e-02, 8.31740930e-02, 8.15979807e-02,
    8.00339475e-02, 7.84819492e-02, 7.69419432e-02, 7.54138887e-02,
    7.38977470e-02, 7.23934809e-02, 7.09010552e-02, 6.94204365e-02,
    6.79515934e-02, 6.64944964e-02, 6.50491178e-02, 6.36154320e-02,
    6.21934154e-02, 6.07830464e-02, 5.93843056e-02, 5.79971756e-02,
    5.66216413e-02, 5.52576897e-02, 5.39053102e-02, 5.25644946e-02,
    5.12352371e-02, 4.99175343e-02, 4.86113856e-02, 4.73167929e-02,
    4.60337611e-02, 4.47622977e-02, 4.35024136e-02, 4.22541224e-02,
    4.10174414e-02, 3.97923910e-02, 3.85789955e-02, 3.73772828e-02,
    3.61872848e-02, 3.50090377e-02, 3.38425822e-02, 3.26879635e-02,
    3.15452322e-02, 3.04144439e-02, 2.92956602e-02, 2.81889488e-02,
    2.70943838e-02, 2.60120466e-02, 2.49420264e-02, 2.38844205e-02,
    2.28393354e-02, 2.18068875e-02, 2.07872041e-02, 1.97804243e-02,
    1.87867007e-02, 1.78062004e-02, 1.68391068e-02, 1.58856218e-02,
    1.49459680e-02, 1.40203914e-02, 1.31091649e-02, 1.22125924e-02,
    1.13310136e-02, 1.04648102e-02, 9.61441364e-03, 8.78031499e-03,
    7.96307744e-03, 7.16335318e-03, 6.38190594e-03, 5.61964221e-03,
    4.87765598e-03, 4.15729512e-03, 3.46026478e-03, 2.78879879e-03,
    2.14596774e-03, 1.53629978e-03, 9.67269282e-04, 4.54134354e-04,]
    , dtype=np.float32)


ziggurat_nor_r = 3.6541528853610087963519472518
ziggurat_nor_inv_r = 0.27366123732975827203338247596
ziggurat_exp_r = 7.6971174701310497140446280481

ziggurat_nor_r_f = np.float32(3.6541528853610087963519472518)
ziggurat_nor_inv_r_f = np.float32(0.27366123732975827203338247596)
ziggurat_exp_r_f = np.float32(7.6971174701310497140446280481)

M_PI = 3.14159265358979323846
INT64_MAX = 9223372036854775807
UINT8_MAX = 255
UINT16_MAX = 65535
UINT32_MAX = 4294967295
UINT64_MAX = 18446744073709551615
LONG_MAX = (1 << ( 8 * ctypes.sizeof(ctypes.c_long) - 1)) - 1

LS2PI = 0.91893853320467267
TWELFTH = 0.083333333333333333333333
